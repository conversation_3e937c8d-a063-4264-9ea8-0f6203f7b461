# Simple 1 sat ordinal creation using the bsv library
import asyncio
from bsv import (
    PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput
)
from bsv.broadcasters.whatsonchain import WhatsOnChainBroadcaster

# Replace with your private key (WIF format)
PRIVATE_KEY = 'L4moXajt2sHNmhcddipPyCb89FBrVgEU5JX1FyaPTKHu3DTy9QgM'

async def create_1_sat_ordinal():
    """
    Creates a simple 1 satoshi ordinal with a normal P2PKH transaction.
    No inscriptions or OP_RETURN data - just a clean 1 sat output.
    """
    try:
        priv_key = PrivateKey(PRIVATE_KEY)

        # Get a UTXO to spend - replace with your actual UTXO
        utxo_info = {
            'txid': '47c20e61438c76b68d24a52d828921c1cb96e19abeda55a720a50f37598b2a4e',
            'hex': '0100000001278fb27a453ade2d34472cc610e66bf74dc54bf8568d2051660229272aca8bac010000006b483045022100943f2d8ca2fabcdaf10aae8a9bcee70887fba2aaa169b6b4f4b3e4e6abe7facb0220689a397c7ec9291a8594dd731acb332f0824c4f032c7d0e992304ab9d0d9fa3c412102d7cc9b0699279a39744f1da107d607e0eb6795a0cb1c04be3dffb90b5efc15d5ffffffff0201000000000000001976a914a3afa11416163d0625a65040a9a9ecdf91fbf1eb88ac9a860100000000001976a914a3afa11416163d0625a65040a9a9ecdf91fbf1eb88ac00000000',
            'output_index': 1
        }

        source_tx = Transaction.from_hex(utxo_info['hex'])

        tx_input = TransactionInput(
            source_transaction=source_tx,
            source_txid=utxo_info['txid'],
            source_output_index=utxo_info['output_index'],
            unlocking_script_template=P2PKH().unlock(priv_key),
        )

        # Create a 1 sat output - this becomes the ordinal
        ordinal_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            satoshis=1  # Just 1 satoshi - this is the ordinal
        )

        # Create change output for remaining BSV
        change_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            change=True
        )

        # Create the transaction with normal P2PKH outputs only
        tx = Transaction([tx_input], [ordinal_output, change_output], version=1)

        # Set a higher fee to ensure miners pick it up quickly
        tx.fee(250)  # 250 satoshis fee
        tx.sign()

        print(f"Transaction fee: {tx.get_fee()} satoshis")

        print(f"About to broadcast 1 sat ordinal creation transaction...")

        # Use WhatsOnChain broadcaster specifically
        woc_broadcaster = WhatsOnChainBroadcaster()
        response = await tx.broadcast(broadcaster=woc_broadcaster)

        # Show the actual response details
        print(f"Broadcast status: {response.status}")

        # Handle different response types
        if response.status == "success":
            print("✅ Transaction successfully broadcast to WhatsOnChain!")
            print(f"Broadcast message: {response.message}")
            print(f"Broadcast txid: {response.txid}")
            print(f"Transaction ID: {tx.txid()}")
            print(f"Raw hex: {tx.hex()}")
        else:
            print(f"❌ Broadcast failed!")
            if hasattr(response, 'code'):
                print(f"Error code: {response.code}")
            if hasattr(response, 'description'):
                print(f"Error description: {response.description}")
            print(f"Transaction ID: {tx.txid()}")
            print(f"Raw hex: {tx.hex()}")
            return None

        # The ordinal is the first output (index 0) of this transaction
        ordinal_id = f"{tx.txid()}:0"
        print(f"1 Sat Ordinal ID: {ordinal_id}")
        print(f"Ordinal Address: {priv_key.address()}")

        return ordinal_id

    except Exception as e:
        print(f"Error creating 1 sat ordinal: {e}")
        raise

if __name__ == "__main__":
    # Create a new 1 sat ordinal
    print("Creating a 1 sat ordinal...")
    ordinal_id = asyncio.run(create_1_sat_ordinal())
    print(f"Successfully created 1 sat ordinal: {ordinal_id}")
    print("\nThis ordinal can be used for future purposes.")
    print("The ordinal is a simple 1 satoshi output with no inscriptions or OP_RETURN data.")

# source venv/bin/activate
# python3 venv/main.py
