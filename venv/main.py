# Ordinal example using the bsv library
import asyncio
from bsv import (
    PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput
)

# Replace with your private key (WIF format)
PRIVATE_KEY = 'KzPKA12eNncJE6qhmHzVAKoUfzaVUpXjYwnCV2ZRx6u7yRy2oQ85'

async def create_ordinal_inscription():
    try:
        priv_key = PrivateKey(PRIVATE_KEY)
        
        # Get a UTXO to spend
        utxo_info = {
            'txid': '8a9016f9e24fa83046234e7517247a57e8cf7fdd5d57150273a1d0bd1068c8e6',
            'hex': '0100000001785b1360fbb708b68174fb715acd3a2981ca5a9eec815a8954f4a79186562a08000000006b483045022100b9861ddb7ad9ebf09abae38fe41d8194416bee678795dea9c8dc19adce7c3e1b022057a4cf39b65d77fae1399e95005cc0124a2ceb67bea78f467427943fa132cdee4121038e9ab307c624114061e21f2af74aae3b04a3359e29f4b060962f3acc961d223dffffffff02f471de15000000001976a9141b877936201f374b364681723992f00b5f34f60c88aca0860100000000001976a9140d2d720ffacd1228fa5bbb8c1a4a545d6aef4a9388ac00000000',
            'output_index': 1
        }
        
        source_tx = Transaction.from_hex(utxo_info['hex'])

        tx_input = TransactionInput(
            source_transaction=source_tx,
            source_txid=utxo_info['txid'],
            source_output_index=utxo_info['output_index'],
            unlocking_script_template=P2PKH().unlock(priv_key),
        )

        # Create a 1 sat output that will become the ordinal
        # This is the key part - a single satoshi output
        ordinal_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            satoshis=1  # Just 1 satoshi - this is the "ordinal"
        )
        
        # Create change output for remaining BSV
        change_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            change=True
        )

        # Create the transaction
        tx = Transaction([tx_input], [ordinal_output, change_output], version=1)

        tx.fee()
        tx.sign()

        print(f"About to broadcast ordinal creation transaction...")
        response = await tx.broadcast()
        print(f"Broadcast response: {response}")

        print(f"Ordinal Transaction ID: {tx.txid()}")
        print(f"Raw hex: {tx.hex()}")
        
        # The ordinal is now the first output (index 0) of this transaction
        ordinal_id = f"{tx.txid()}:0"
        print(f"Ordinal ID: {ordinal_id}")
        
        return ordinal_id
        
    except Exception as e:
        print(f"Error creating ordinal: {e}")
        raise

# Example of inscribing content to an ordinal
async def inscribe_ordinal(ordinal_id, content_type, content, utxo_info, funding_utxo_info=None):
    try:
        priv_key = PrivateKey(PRIVATE_KEY)
        source_tx = Transaction.from_hex(utxo_info['hex'])

        # Create inputs list starting with the ordinal
        inputs = [
            TransactionInput(
                source_transaction=source_tx,
                source_txid=utxo_info['txid'],
                source_output_index=utxo_info['output_index'],
                unlocking_script_template=P2PKH().unlock(priv_key),
            )
        ]
        
        # Add funding input if provided
        if funding_utxo_info:
            funding_tx = Transaction.from_hex(funding_utxo_info['hex'])
            funding_input = TransactionInput(
                source_transaction=funding_tx,
                source_txid=funding_utxo_info['txid'],
                source_output_index=funding_utxo_info['output_index'],
                unlocking_script_template=P2PKH().unlock(priv_key),
            )
            inputs.append(funding_input)

        # Create the inscription data
        from bsv import OpReturn
        
        # Format for inscription data (simplified)
        inscription_data = [
            "ord",  # Prefix for ordinal protocol
            content_type,  # e.g., "image/png", "text/plain"
            content  # The actual content bytes
        ]
        
        # Create outputs
        outputs = [
            # OP_RETURN output with inscription data
            TransactionOutput(
                locking_script=OpReturn().lock(inscription_data),
                satoshis=0  # OP_RETURN outputs have 0 satoshis
            ),
            # The ordinal itself - still 1 sat
            TransactionOutput(
                locking_script=P2PKH().lock(priv_key.address()),
                satoshis=1  # Keep it as 1 satoshi
            ),
            # Change output
            TransactionOutput(
                locking_script=P2PKH().lock(priv_key.address()),
                change=True
            )
        ]

        # Create the transaction
        tx = Transaction(inputs, outputs, version=1)

        tx.fee()
        tx.sign()

        print(f"About to broadcast ordinal inscription transaction...")
        response = await tx.broadcast()
        print(f"Broadcast response: {response}")

        print(f"Inscription Transaction ID: {tx.txid()}")
        print(f"Raw hex: {tx.hex()}")
        
        # The ordinal is now the second output (index 1) of this transaction
        new_ordinal_id = f"{tx.txid()}:1"
        print(f"New Ordinal ID: {new_ordinal_id}")
        
        return new_ordinal_id
        
    except Exception as e:
        print(f"Error inscribing ordinal: {e}")
        raise

if __name__ == "__main__":
    # Uncomment this if you want to create a new ordinal
    # ordinal_id = asyncio.run(create_ordinal_inscription())
    # print(f"Created ordinal: {ordinal_id}")
    
    # Use your existing ordinal
    ordinal_id = "992b2ed985ceb36d71bac3ecf566dfeda8480a5aab3c36dceb13f2b5ed03ad2f:0"
    
    # Get the raw transaction hex from WhatsonChain or similar explorer
    # This is the transaction that created your ordinal
    raw_tx_hex = "0100000001e6c86810bdd0a1730215575ddd7fcfe8577a2417754e234630a84fe2f916908a010000006b4830450221009ce821aa0930ab7b91077fda0443f41762e9d3d830aef8a5800882b6eaab2c2c022008a4300e56eb2a2be1e24e372ba09111799fafcc726166ccf68e5e19b6ba75fc412102d640643add8a245a5024b1902a7ce999b52bc6a1b7f88a1da4d2ca3e90987af2ffffffff0201000000000000001976a9140d2d720ffacd1228fa5bbb8c1a4a545d6aef4a9388ac9d860100000000001976a9140d2d720ffacd1228fa5bbb8c1a4a545d6aef4a9388ac00000000"
    
    # Add a funding UTXO with sufficient balance to pay for the transaction fee
    funding_utxo_info = {
        'txid': '992b2ed985ceb36d71bac3ecf566dfeda8480a5aab3c36dceb13f2b5ed03ad2f',  # Replace with your funding UTXO
        'hex': '0100000001e6c86810bdd0a1730215575ddd7fcfe8577a2417754e234630a84fe2f916908a010000006b4830450221009ce821aa0930ab7b91077fda0443f41762e9d3d830aef8a5800882b6eaab2c2c022008a4300e56eb2a2be1e24e372ba09111799fafcc726166ccf68e5e19b6ba75fc412102d640643add8a245a5024b1902a7ce999b52bc6a1b7f88a1da4d2ca3e90987af2ffffffff0201000000000000001976a9140d2d720ffacd1228fa5bbb8c1a4a545d6aef4a9388ac9d860100000000001976a9140d2d720ffacd1228fa5bbb8c1a4a545d6aef4a9388ac00000000',  # Replace with actual hex
        'output_index': 1  # Replace with correct output index
    }
    
    # Now inscribe content to the ordinal with additional funding
    # Here's our inscribed OP_RETURN data
    asyncio.run(inscribe_ordinal(
        ordinal_id=ordinal_id,
        content_type="text/plain",
        content="red, yellow",
        utxo_info={
            'txid': ordinal_id.split(":")[0],
            'hex': raw_tx_hex,
            'output_index': int(ordinal_id.split(":")[1])
        },
        funding_utxo_info=funding_utxo_info
    ))
