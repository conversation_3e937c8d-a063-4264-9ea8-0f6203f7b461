# Simple 1 sat ordinal creation using the bsv library
import asyncio
from bsv import (
    PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput
)

# Replace with your private key (WIF format)
PRIVATE_KEY = 'L4moXajt2sHNmhcddipPyCb89FBrVgEU5JX1FyaPTKHu3DTy9QgM'

async def create_1_sat_ordinal():
    """
    Creates a simple 1 satoshi ordinal with a normal P2PKH transaction.
    No inscriptions or OP_RETURN data - just a clean 1 sat output.
    """
    try:
        priv_key = PrivateKey(PRIVATE_KEY)

        # Get a UTXO to spend - replace with your actual UTXO
        utxo_info = {
            'txid': 'ac8bca2a2729026651208d56f84bc54df76be610c62c47342dde3a457ab28f27',
            'hex': '0100000001380dce45e4f26abe96ffee33cb703c5e41cf0022f670c4e207fc64b49a2c7c6b000000006a47304402206c81ef36521b25d62e05dde2d1921a04085f6e282d1ff24511c1d8a3afc810e60220344ca5fbbf21a39e6b78991fc8dfe656c0f7e607816ee150edf1c3ffa33c8804412102d7cc9b0699279a39744f1da107d607e0eb6795a0cb1c04be3dffb90b5efc15d5ffffffff0201000000000000001976a914a3afa11416163d0625a65040a9a9ecdf91fbf1eb88ac9d860100000000001976a914a3afa11416163d0625a65040a9a9ecdf91fbf1eb88ac00000000',
            'output_index': 1
        }

        source_tx = Transaction.from_hex(utxo_info['hex'])

        tx_input = TransactionInput(
            source_transaction=source_tx,
            source_txid=utxo_info['txid'],
            source_output_index=utxo_info['output_index'],
            unlocking_script_template=P2PKH().unlock(priv_key),
        )

        # Create a 1 sat output - this becomes the ordinal
        ordinal_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            satoshis=1  # Just 1 satoshi - this is the ordinal
        )

        # Create change output for remaining BSV
        change_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            change=True
        )

        # Create the transaction with normal P2PKH outputs only
        tx = Transaction([tx_input], [ordinal_output, change_output], version=1)

        tx.fee()
        tx.sign()

        print(f"About to broadcast 1 sat ordinal creation transaction...")
        response = await tx.broadcast()
        print(f"Broadcast response: {response}")

        print(f"Transaction ID: {tx.txid()}")
        print(f"Raw hex: {tx.hex()}")

        # The ordinal is the first output (index 0) of this transaction
        ordinal_id = f"{tx.txid()}:0"
        print(f"1 Sat Ordinal ID: {ordinal_id}")
        print(f"Ordinal Address: {priv_key.address()}")

        return ordinal_id

    except Exception as e:
        print(f"Error creating 1 sat ordinal: {e}")
        raise

if __name__ == "__main__":
    # Create a new 1 sat ordinal
    print("Creating a 1 sat ordinal...")
    ordinal_id = asyncio.run(create_1_sat_ordinal())
    print(f"Successfully created 1 sat ordinal: {ordinal_id}")
    print("\nThis ordinal can be used for future purposes.")
    print("The ordinal is a simple 1 satoshi output with no inscriptions or OP_RETURN data.")

# source venv/bin/activate
# python3 venv/main.py
